package wspkg

import (
	"errors"
	"fmt"
	"log"
	"sync"
	"time"

	"github.com/Terracode-Dev/ION_SEC_DOOR_WSS/internal/code"
	"github.com/google/uuid"
)

type DoorClient struct {
	DoorId       int64
	Name         string
	BranchId     int
	isOpen       bool
	qrIn         *QRClient
	qrOut        *QRClient
	onlineTime   time.Time
	lastCmd      time.Time
	secKey       string
	isprocessing bool
	isError      bool
	state        int
	mu           sync.RWMutex // Add mutex for field protection
}

func NewDoorClient(doorId int64, name string, branchId int) *DoorClient {
	secKey := uuid.New().String()

	return &DoorClient{
		DoorId:       doorId,
		Name:         name,
		BranchId:     branchId,
		isOpen:       false,
		qrIn:         nil,
		qrOut:        nil,
		isprocessing: false,
		onlineTime:   time.Now(),
		secKey:       secKey,
		state:        code.IDEL,
	}
}

func (d *DoorClient) SetQRIn(client *QRClient) error {
	d.mu.Lock()
	defer d.mu.Unlock()
	if client.CheckType() == "qr-in" {
		d.qrIn = client
		return nil
	}

	return errors.New("clinet need to be [in] type")
}

func (d *DoorClient) SetQROut(client *QRClient) error {
	d.mu.Lock()
	defer d.mu.Unlock()
	if client.CheckType() == "qr-out" {
		d.qrOut = client
		return nil
	}
	return errors.New("clinet need to be [out] type")
}

func (d *DoorClient) SetIsProcessing(b bool) {
	d.mu.Lock()
	defer d.mu.Unlock()
	d.isprocessing = b
}

func (d *DoorClient) CheckIsProcessing() bool {
	d.mu.RLock()
	defer d.mu.RUnlock()
	return d.isprocessing
}

func (d *DoorClient) SetIsOpen(b bool) {
	d.mu.Lock()
	defer d.mu.Unlock()
	d.isOpen = b
}

func (d *DoorClient) IsOpen() bool {
	d.mu.RLock()
	defer d.mu.RUnlock()
	return d.isOpen
}

func (d *DoorClient) SetState(state int) {
	d.mu.Lock()
	defer d.mu.Unlock()
	d.state = state
}

func (d *DoorClient) GetState() int {
	d.mu.RLock()
	defer d.mu.RUnlock()
	return d.state
}

func (d *DoorClient) GetSecKey() string {
	d.mu.RLock()
	defer d.mu.RUnlock()
	return d.secKey
}

func (d *DoorClient) GetOnlineTime() time.Time {
	d.mu.RLock()
	defer d.mu.RUnlock()
	return d.onlineTime
}

func (d *DoorClient) GetLastCmd() time.Time {
	d.mu.RLock()
	defer d.mu.RUnlock()
	return d.lastCmd
}

func (d *DoorClient) SetLastCmd(t time.Time) {
	d.mu.Lock()
	defer d.mu.Unlock()
	d.lastCmd = t
}

func (d *DoorClient) GetQRIn() *QRClient {
	d.mu.RLock()
	defer d.mu.RUnlock()
	if d.qrIn == nil || !d.qrIn.CheckValid() {
		return nil
	}
	return d.qrIn
}

func (d *DoorClient) GetQROut() *QRClient {
	d.mu.RLock()
	defer d.mu.RUnlock()
	if d.qrOut == nil || !d.qrOut.CheckValid() {
		return nil
	}
	return d.qrOut
}

func (d *DoorClient) GenNewSecKey() {
	d.mu.Lock()
	defer d.mu.Unlock()
	secKey := uuid.New().String()
	d.secKey = secKey
}

type QRClient struct {
	Id           string
	Typ          string
	BranchId     int
	doorId       int64
	isprocessing bool
	isError      bool
	onlineTime   time.Time
	state        int
	valid        bool
	mu           sync.RWMutex // Add mutex for field protection
}

func NewQRClient(typ string, branchId int, Id string, door int64) *QRClient {
	return &QRClient{
		Id:           Id,
		Typ:          typ,
		BranchId:     branchId,
		doorId:       door,
		isprocessing: false,
		isError:      false,
		onlineTime:   time.Now(),
		state:        code.IDEL,
		valid:        true,
	}
}

func (q *QRClient) CheckType() string {
	return q.Typ
}

func (q *QRClient) CheckValid() bool {
	q.mu.RLock()
	defer q.mu.RUnlock()
	return q.valid
}

func (q *QRClient) SetValid(b bool) {
	q.mu.Lock()
	defer q.mu.Unlock()
	q.valid = b
}

func (q *QRClient) SetIsProcessing(b bool) {
	q.mu.Lock()
	defer q.mu.Unlock()
	q.isprocessing = b
}

func (q *QRClient) CheckIsProcessing() bool {
	q.mu.RLock()
	defer q.mu.RUnlock()
	return q.isprocessing
}

func (q *QRClient) SetIsError(b bool) {
	q.mu.Lock()
	defer q.mu.Unlock()
	q.isError = b
}

func (q *QRClient) CheckIsError() bool {
	q.mu.RLock()
	defer q.mu.RUnlock()
	return q.isError
}

func (q *QRClient) SetState(state int) {
	q.mu.Lock()
	defer q.mu.Unlock()
	q.state = state
}

func (q *QRClient) GetState() int {
	q.mu.RLock()
	defer q.mu.RUnlock()
	return q.state
}

func (q *QRClient) GetOnlineTime() time.Time {
	q.mu.RLock()
	defer q.mu.RUnlock()
	return q.onlineTime
}

type ClientState struct {
	DoorLocks map[string]*DoorClient
	QRDevice  map[string]*QRClient
	mut       sync.RWMutex
}

func NewClientState() *ClientState {
	return &ClientState{
		DoorLocks: make(map[string]*DoorClient, 5),
		QRDevice:  make(map[string]*QRClient, 10),
		mut:       sync.RWMutex{},
	}
}

func (c *ClientState) CreateDoorClient(clientId string, doorId int64, name string, branchId int) {
	c.mut.Lock()
	defer c.mut.Unlock()

	doorClient := NewDoorClient(doorId, name, branchId)
	c.DoorLocks[clientId] = doorClient
}

func (c *ClientState) GetDoorClient(clientId string) (*DoorClient, error) {
	c.mut.RLock()
	defer c.mut.RUnlock()

	res, ok := c.DoorLocks[clientId]
	if !ok {
		return nil, errors.New("door client not found")
	}

	return res, nil
}

func (c *ClientState) CreateQRClient(clientId string, typ string, branchId int, door int64) {
	c.mut.Lock()
	defer c.mut.Unlock()

	qrClient := NewQRClient(typ, branchId, clientId, door)
	c.QRDevice[clientId] = qrClient
}

func (c *ClientState) CreateQRClientWithDoorCheckin(clientId string, typ string, branchId int, door int64) {
	c.mut.Lock()
	defer c.mut.Unlock()

	qrClient := NewQRClient(typ, branchId, clientId, door)
	c.QRDevice[clientId] = qrClient

	// Find and update door client with proper synchronization
	for _, doorClient := range c.DoorLocks {
		if doorClient.DoorId == door {
			// Use doorClient's mutex to safely set QR associations
			doorClient.mu.Lock()
			switch typ {
			case "qr-in":
				doorClient.qrIn = qrClient
			case "qr-out":
				doorClient.qrOut = qrClient
			default:
				log.Print("There is issue @ clientData typ")
			}
			doorClient.mu.Unlock()
			break
		}
	}
}

func (c *ClientState) GetQRClient(clientId string) (*QRClient, error) {
	c.mut.RLock()
	defer c.mut.RUnlock()

	res, ok := c.QRDevice[clientId]
	if !ok {
		return nil, errors.New("qr client not found")
	}

	if !res.CheckValid() {
		return nil, errors.New("qr client is not valid")
	}

	return res, nil
}

func (c *ClientState) RemoveDoorClient(clientId string) {
	c.mut.Lock()
	defer c.mut.Unlock()
	if _, ok := c.DoorLocks[clientId]; !ok {
		return
	}

	delete(c.DoorLocks, clientId)
}

func (c *ClientState) RemoveQRClient(clientId string) {
	c.mut.Lock()
	defer c.mut.Unlock()

	qrClient, ok := c.QRDevice[clientId]
	if !ok {
		return
	}

	// Mark as invalid first
	qrClient.SetValid(false)

	// Remove QR client association from door clients with proper synchronization
	for _, doorClient := range c.DoorLocks {
		// Use doorClient's mutex to safely access and modify qrIn/qrOut
		doorClient.mu.Lock()
		if doorClient.qrIn != nil && doorClient.qrIn.Id == clientId {
			doorClient.qrIn = nil
		}
		if doorClient.qrOut != nil && doorClient.qrOut.Id == clientId {
			doorClient.qrOut = nil
		}
		doorClient.mu.Unlock()
	}

	// Remove from map
	delete(c.QRDevice, clientId)
}

// SafelyAssociateQRClient safely associates a QR client with a door client
func (c *ClientState) SafelyAssociateQRClient(doorId int64, qrClientId string, qrType string) error {
	c.mut.Lock()
	defer c.mut.Unlock()

	// Find the door client
	var doorClient *DoorClient
	for _, door := range c.DoorLocks {
		if door.DoorId == doorId {
			doorClient = door
			break
		}
	}

	if doorClient == nil {
		return fmt.Errorf("door client not found for door ID: %d", doorId)
	}

	// Find the QR client
	qrClient, ok := c.QRDevice[qrClientId]
	if !ok {
		return fmt.Errorf("QR client not found: %s", qrClientId)
	}

	// Use doorClient's mutex to safely check and set QR associations
	doorClient.mu.Lock()
	defer doorClient.mu.Unlock()

	switch qrType {
	case "qr-in":
		if doorClient.qrIn != nil {
			return fmt.Errorf("qr-in already exists for door %d", doorId)
		}
		doorClient.qrIn = qrClient
	case "qr-out":
		if doorClient.qrOut != nil {
			return fmt.Errorf("qr-out already exists for door %d", doorId)
		}
		doorClient.qrOut = qrClient
	default:
		return fmt.Errorf("invalid QR type: %s", qrType)
	}

	return nil
}

func (c *ClientState) GetDoorClientsByBranch(id int) []*DoorClient {
	c.mut.RLock()
	defer c.mut.RUnlock()

	var res []*DoorClient
	for _, door := range c.DoorLocks {
		if door.BranchId == id {
			res = append(res, door)
		}
	}

	return res
}

func (c *ClientState) GetQRClientsByBranch(id int) []*QRClient {
	c.mut.RLock()
	defer c.mut.RUnlock()
	var res []*QRClient

	for _, qr := range c.QRDevice {
		if qr.BranchId == id && qr.CheckValid() {
			res = append(res, qr)
		}
	}

	return res
}

func (c *ClientState) WithDoorClient(doorId int64, fn func(*DoorClient)) {
	// Find the door client first
	c.mut.RLock()
	var targetDoor *DoorClient
	for _, door := range c.DoorLocks {
		if door.DoorId == doorId {
			targetDoor = door
			break
		}
	}
	c.mut.RUnlock()

	// Execute callback without holding the ClientState mutex to prevent deadlocks
	if targetDoor != nil {
		fn(targetDoor)
	}
}

func (c *ClientState) GetDoorClientsQRINOUTAvalable(doorId int64, typ string) bool {
	c.mut.RLock()
	defer c.mut.RUnlock()

	for _, door := range c.DoorLocks {
		if door.DoorId == doorId {
			// Use doorClient's mutex to safely read QR associations
			door.mu.RLock()
			var available bool
			switch typ {
			case "qr-in":
				available = (door.qrIn == nil)
			case "qr-out":
				available = (door.qrOut == nil)
			default:
				available = false
			}
			door.mu.RUnlock()
			return available
		}
	}
	return false
}

func (c *ClientState) CheckDoorExist(doorId int64) bool {
	c.mut.RLock()
	defer c.mut.RUnlock()

	for _, door := range c.DoorLocks {
		if door.DoorId == doorId {
			return true
		}
	}
	return false
}

// GetDoorQRStatus returns the current QR client status for a door in a thread-safe manner
func (c *ClientState) GetDoorQRStatus(doorId int64) (qrInConnected bool, qrOutConnected bool, exists bool) {
	c.mut.RLock()
	defer c.mut.RUnlock()

	for _, door := range c.DoorLocks {
		if door.DoorId == doorId {
			door.mu.RLock()
			qrInConnected = (door.qrIn != nil && door.qrIn.CheckValid())
			qrOutConnected = (door.qrOut != nil && door.qrOut.CheckValid())
			door.mu.RUnlock()
			return qrInConnected, qrOutConnected, true
		}
	}
	return false, false, false
}

// AtomicQRClientOperation performs an atomic operation on QR client associations
func (c *ClientState) AtomicQRClientOperation(doorId int64, operation func(*DoorClient) error) error {
	c.mut.RLock()
	var targetDoor *DoorClient
	for _, door := range c.DoorLocks {
		if door.DoorId == doorId {
			targetDoor = door
			break
		}
	}
	c.mut.RUnlock()

	if targetDoor == nil {
		return fmt.Errorf("door client not found for door ID: %d", doorId)
	}

	// Execute operation with door client's mutex held
	targetDoor.mu.Lock()
	defer targetDoor.mu.Unlock()
	return operation(targetDoor)
}

// GetDoorClientsByDoorId returns a door client by its door ID (for testing compatibility)
func (c *ClientState) GetDoorClientsByDoorId(doorId int64) *DoorClient {
	c.mut.RLock()
	defer c.mut.RUnlock()

	for _, door := range c.DoorLocks {
		if door.DoorId == doorId {
			return door
		}
	}
	return nil
}
