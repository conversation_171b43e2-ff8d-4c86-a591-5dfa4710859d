package wspkg

import (
	"context"
	"crypto/sha256"
	"encoding/hex"
	"encoding/json"
	"fmt"
	"log"
	"sort"
	"strings"
	"sync"
	"time"

	"github.com/redis/go-redis/v9"
)

// Message represents the structure of messages we'll publish
// type Message struct {
// 	ID        string    `json:"id"`
// 	Type      string    `json:"type"`
// 	Payload   string    `json:"payload"`
// 	Timestamp time.Time `json:"timestamp"`
// }

// PubSubManager handles Redis pub/sub operations
type PubSubManager struct {
	client      *redis.Client
	ctx         context.Context
	cancel      context.CancelFunc
	subscribers map[string]*redis.PubSub
	mu          sync.RWMutex
	reconnectCh chan struct{}
	closed      bool
}

// NewPubSubManager creates a new pub/sub manager
func NewPubSubManager(redisURL string) (*PubSubManager, error) {
	opts, err := redis.ParseURL(redisURL)
	if err != nil {
		return nil, fmt.Errorf("failed to parse Redis URL: %w", err)
	}

	client := redis.NewClient(opts)

	// Test connection
	ctx, cancel := context.WithCancel(context.Background())
	if err := client.Ping(ctx).Err(); err != nil {
		cancel()
		return nil, fmt.Errorf("failed to connect to Redis: %w", err)
	}

	pm := &PubSubManager{
		client:      client,
		ctx:         ctx,
		cancel:      cancel,
		subscribers: make(map[string]*redis.PubSub),
		reconnectCh: make(chan struct{}, 1),
		closed:      false,
	}

	// Start connection health monitoring
	go pm.healthMonitor()

	return pm, nil
}

// healthMonitor monitors Redis connection health and handles reconnection
func (pm *PubSubManager) healthMonitor() {
	ticker := time.NewTicker(30 * time.Second)
	defer ticker.Stop()

	for {
		select {
		case <-pm.ctx.Done():
			return
		case <-ticker.C:
			if err := pm.client.Ping(pm.ctx).Err(); err != nil {
				log.Printf("Redis connection lost: %v", err)
				select {
				case pm.reconnectCh <- struct{}{}:
				default:
				}
			}
		case <-pm.reconnectCh:
			pm.handleReconnection()
		}
	}
}

// handleReconnection attempts to reconnect to Redis
func (pm *PubSubManager) handleReconnection() {
	pm.mu.Lock()
	defer pm.mu.Unlock()

	if pm.closed {
		return
	}

	log.Printf("Attempting Redis reconnection...")

	// Close existing subscriptions
	for key, sub := range pm.subscribers {
		sub.Close()
		delete(pm.subscribers, key)
	}

	// Test connection
	if err := pm.client.Ping(pm.ctx).Err(); err != nil {
		log.Printf("Redis reconnection failed: %v", err)
		// Retry after delay
		go func() {
			time.Sleep(5 * time.Second)
			select {
			case pm.reconnectCh <- struct{}{}:
			default:
			}
		}()
		return
	}

	log.Printf("Redis reconnection successful")
}

// Publish publishes a message to a channel
func (pm *PubSubManager) Publish(channel string, msg Message) error {
	msgBytes, err := json.Marshal(msg)
	if err != nil {
		return fmt.Errorf("failed to marshal message: %w", err)
	}

	err = pm.client.Publish(pm.ctx, channel, msgBytes).Err()
	if err != nil {
		return fmt.Errorf("failed to publish message: %w", err)
	}

	return nil
}

// generateSubscriptionKey creates a unique key for subscription tracking
func (pm *PubSubManager) generateSubscriptionKey(channels []string) string {
	// Sort channels to ensure consistent key generation
	sorted := make([]string, len(channels))
	copy(sorted, channels)
	sort.Strings(sorted)

	// Create hash of sorted channels to avoid collisions
	h := sha256.New()
	h.Write([]byte(strings.Join(sorted, ",")))
	return hex.EncodeToString(h.Sum(nil))
}

// Subscribe subscribes to one or more channels
func (pm *PubSubManager) Subscribe(channels []string, handler func(channel string, msg *Message)) error {
	// Check if manager is closed without holding lock for long
	pm.mu.RLock()
	if pm.closed {
		pm.mu.RUnlock()
		return fmt.Errorf("pubsub manager is closed")
	}
	pm.mu.RUnlock()

	pubsub := pm.client.Subscribe(pm.ctx, channels...)

	// Store subscriber for cleanup with unique key
	key := pm.generateSubscriptionKey(channels)
	pm.mu.Lock()
	// Double-check closed status while holding write lock
	if pm.closed {
		pm.mu.Unlock()
		pubsub.Close()
		return fmt.Errorf("pubsub manager is closed")
	}
	pm.subscribers[key] = pubsub
	pm.mu.Unlock()

	// Wait for subscription confirmation
	_, err := pubsub.Receive(pm.ctx)
	if err != nil {
		pm.mu.Lock()
		delete(pm.subscribers, key)
		pm.mu.Unlock()
		pubsub.Close()
		return fmt.Errorf("failed to confirm subscription: %w", err)
	}

	log.Printf("Subscribed to channels: %v", channels)

	// Handle messages in a goroutine with panic recovery
	go func() {
		defer func() {
			// Recover from panics
			if r := recover(); r != nil {
				log.Printf("Panic in subscription handler: %v", r)
			}

			// Cleanup
			pm.mu.Lock()
			delete(pm.subscribers, key)
			pm.mu.Unlock()
			pubsub.Close()
		}()

		ch := pubsub.Channel()
		for {
			select {
			case <-pm.ctx.Done():
				log.Printf("Subscription context cancelled for channels: %v", channels)
				return
			case msg, ok := <-ch:
				if !ok {
					log.Printf("Subscription channel closed for channels: %v", channels)
					return
				}

				var parsedMsg Message
				if err := json.Unmarshal([]byte(msg.Payload), &parsedMsg); err != nil {
					log.Printf("Failed to unmarshal message from channel %s: %v, payload: %s",
						msg.Channel, err, msg.Payload)
					continue
				}

				// Call handler with panic recovery and deadlock prevention
				// Use a separate goroutine to prevent potential deadlocks if handler
				// tries to call back into PubSubManager methods
				go func(channel string, message *Message) {
					defer func() {
						if r := recover(); r != nil {
							log.Printf("Panic in message handler for channel %s: %v", channel, r)
						}
					}()
					handler(channel, message)
				}(msg.Channel, &parsedMsg)
			}
		}
	}()

	return nil
}

// SubscribePattern subscribes to channels matching a pattern
func (pm *PubSubManager) SubscribePattern(pattern string, handler func(channel string, msg Message)) error {
	pubsub := pm.client.PSubscribe(pm.ctx, pattern)

	// Store subscriber for cleanup
	pm.mu.Lock()
	pm.subscribers[pattern] = pubsub
	pm.mu.Unlock()

	// Wait for subscription confirmation
	_, err := pubsub.Receive(pm.ctx)
	if err != nil {
		return fmt.Errorf("failed to confirm pattern subscription: %w", err)
	}

	log.Printf("Subscribed to pattern: %s", pattern)

	// Handle messages in a goroutine
	go func() {
		defer func() {
			pm.mu.Lock()
			delete(pm.subscribers, pattern)
			pm.mu.Unlock()
			pubsub.Close()
		}()

		ch := pubsub.Channel()
		for msg := range ch {
			var parsedMsg Message
			if err := json.Unmarshal([]byte(msg.Payload), &parsedMsg); err != nil {
				log.Printf("Failed to unmarshal message: %v", err)
				continue
			}

			handler(msg.Channel, parsedMsg)
		}
	}()

	return nil
}

// Close closes all subscriptions and the Redis client
func (pm *PubSubManager) Close() error {
	pm.mu.Lock()
	defer pm.mu.Unlock()

	if pm.closed {
		return nil
	}

	pm.closed = true

	// Cancel context to stop all goroutines
	if pm.cancel != nil {
		pm.cancel()
	}

	// Close all subscribers
	for _, sub := range pm.subscribers {
		sub.Close()
	}

	// Clear subscribers map
	pm.subscribers = make(map[string]*redis.PubSub)

	// Close Redis client
	return pm.client.Close()
}

// GetStats returns basic statistics about active subscriptions
func (pm *PubSubManager) GetStats() map[string]interface{} {
	pm.mu.RLock()
	defer pm.mu.RUnlock()

	return map[string]interface{}{
		"active_subscriptions": len(pm.subscribers),
		"redis_pool_stats":     pm.client.PoolStats(),
	}
}
