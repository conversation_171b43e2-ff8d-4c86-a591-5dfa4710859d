package wspkg

func (W *WebSocketCtx) WebSocketEventRoute() {
	// Register the WebSocket event route
	W.manger.router.On("isOpen", W.OnIsOpen)
}

func (W *WebSocketCtx) OnIsOpen(c Ctx) error {
	doorClient, err := W.ClientState.GetDoorClient(c.GetClientID())
	if err != nil {
		W.manger.logger.Error(
			"Door client not found for isOpen event",
			Field{Key: "client_id", Value: c.GetClientID()},
			Field{Key: "error", Value: err},
		)
		return nil // Don't return error to prevent disconnection
	}

	data := map[string]interface{}{"isOpen": doorClient.IsOpen()}
	msg, err := NewMessageFromJSON("isOpen", data)
	if err != nil {
		W.manger.logger.Error(
			"Error creating message for isOpen event",
			Field{Key: "client_id", Value: c.Get<PERSON>lient<PERSON>()},
			Field{Key: "error", Value: err},
		)
		return nil // Don't return error to prevent disconnection
	}

	c.SendTo<PERSON>lient(msg)
	W.manger.logger.Debug(
		"isOpen event handled successfully",
		Field{Key: "client_id", Value: c.GetClientID()},
		Field{Key: "isOpen", Value: doorClient.IsOpen()},
	)
	return nil
}
