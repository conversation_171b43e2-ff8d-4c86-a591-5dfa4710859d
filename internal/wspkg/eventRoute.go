package wspkg

func (W *WebSocketCtx) WebSocketEventRoute() {
	// Register the WebSocket event route
	W.manger.router.On("isOpen", W.OnIsOpen)
}

func (W *WebSocketCtx) OnIsOpen(c Ctx) error {
	doorClient, err := W.ClientState.GetDoorClient(c.GetClientID())
	if err != nil {
		return nil
	}

	data := map[string]interface{}{"isOpen": doorClient.isOpen}
	msg, err := NewMessageFromJSON("isOpen", data)
	if err != nil {
		W.manger.logger.Error(
			"Error creating message for isOpen event",
			Field{Key: "error", Value: err},
		)
	}
	c.SendTo<PERSON>lient(msg)
	return nil
}
